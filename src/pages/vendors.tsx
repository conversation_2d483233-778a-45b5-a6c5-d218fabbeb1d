import { trpc } from "@/utils/trpc";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@/components/ui/table";
import { ChevronLeft, ChevronRight, RefreshCw, Building, MapPin, Phone, Mail, Trash2, MoreHorizontal } from "lucide-react";
import { VendorDetailsDrawer } from "@/components/VendorDetailsDrawer";
import { VendorTableHeader, SortConfig } from "@/components/VendorTableHeader";
import { DashboardLayout } from "@/components/DashboardLayout";
import { SearchInput } from "@/components/SearchInput";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { currency } from "@/modules/core/currency";

export default function VendorsPage() {
  const router = useRouter();
  const [limit, setLimit] = useState(25);
  const [cursor, setCursor] = useState<string | undefined>(undefined);
  const [cursors, setCursors] = useState<string[]>([]);
  const [selectedVendorId, setSelectedVendorId] = useState<string | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [vendorToDelete, setVendorToDelete] = useState<{ id: string; name: string } | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [sortConfig, setSortConfig] = useState<SortConfig>({ field: "name", direction: "asc" });

  // Handle vendorId from URL query parameter
  useEffect(() => {
    if (router.query.vendorId && typeof router.query.vendorId === "string") {
      setSelectedVendorId(router.query.vendorId);
      setDrawerOpen(true);
    }
  }, [router.query.vendorId]);

  // Fetch vendor stats
  const stats = trpc.vendors.getStats.useQuery();

  // Fetch vendors with pagination and search
  const vendors = trpc.vendors.getAll.useQuery({
    limit,
    ...(cursor && { cursor }),
    ...(searchTerm.trim() && { search: searchTerm.trim() }),
    ...(sortConfig.direction && { sortField: sortConfig.field as any, sortDirection: sortConfig.direction }),
  });

  // Delete vendor mutation
  const deleteVendor = trpc.vendors.delete.useMutation({
    onSuccess: () => {
      vendors.refetch();
      stats.refetch();
      setDeleteDialogOpen(false);
      setVendorToDelete(null);
    },
    onError: (error) => {
      console.error("Failed to delete vendor:", error);
      // You could add a toast notification here
    },
  });

  const handleNextPage = () => {
    if (vendors.data?.nextCursor) {
      setCursors((prev) => [...prev, cursor || ""]);
      setCursor(vendors.data.nextCursor);
    }
  };

  const handlePrevPage = () => {
    if (cursors.length > 0) {
      const newCursors = [...cursors];
      const prevCursor = newCursors.pop();
      setCursors(newCursors);
      setCursor(prevCursor === "" ? undefined : prevCursor);
    }
  };

  const handleLimitChange = (newLimit: string) => {
    setLimit(Number(newLimit));
    setCursor(undefined);
    setCursors([]);
  };

  const handleSearchChange = (search: string) => {
    console.log("handleSearchChange", search);
    setSearchTerm(search);
    setCursor(undefined);
    setCursors([]);
  };

  const handleVendorClick = (vendorId: string) => {
    setSelectedVendorId(vendorId);
    setDrawerOpen(true);
  };

  const handleDrawerOpenChange = (open: boolean) => {
    setDrawerOpen(open);
    if (!open) {
      setSelectedVendorId(null);
    }
  };

  const handleDeleteClick = (vendor: { id: string; name: string }, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent row click
    setVendorToDelete(vendor);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (vendorToDelete) {
      deleteVendor.mutate({ id: vendorToDelete.id });
    }
  };

  const handleSortChange = (newSortConfig: SortConfig) => {
    setSortConfig(newSortConfig);
    // Reset pagination when sorting changes
    setCursor(undefined);
    setCursors([]);
  };

  const handleNavigateNext = () => {
    if (!vendors.data?.items || !selectedVendorId) return;

    const currentIndex = vendors.data.items.findIndex((vendor) => vendor.id === selectedVendorId);
    if (currentIndex < vendors.data.items.length - 1) {
      const nextVendor = vendors.data.items[currentIndex + 1];
      setSelectedVendorId(nextVendor.id);
    }
  };

  const handleNavigatePrevious = () => {
    if (!vendors.data?.items || !selectedVendorId) return;

    const currentIndex = vendors.data.items.findIndex((vendor) => vendor.id === selectedVendorId);
    if (currentIndex > 0) {
      const previousVendor = vendors.data.items[currentIndex - 1];
      setSelectedVendorId(previousVendor.id);
    }
  };

  const getNavigationState = () => {
    if (!vendors.data?.items || !selectedVendorId) {
      return { hasNext: false, hasPrevious: false };
    }

    const currentIndex = vendors.data.items.findIndex((vendor) => vendor.id === selectedVendorId);
    return {
      hasNext: currentIndex < vendors.data.items.length - 1,
      hasPrevious: currentIndex > 0,
    };
  };

  const { hasNext, hasPrevious } = getNavigationState();

  // Reset pagination when search term changes
  useEffect(() => {
    setCursor(undefined);
    setCursors([]);
  }, [searchTerm]);

  const calculateTotalAmount = (invoices: any) => {
    return invoices.reduce((sum: number, invoice: any) => sum + Number(invoice.amountGross), 0);
  };

  const getMostCommonInvoiceKind = (invoices: any[]) => {
    if (invoices.length === 0) return "UNKNOWN";
    const typeCounts: Record<string, number> = {};
    invoices.forEach((inv) => {
      const type = inv.importItem?.importItemType || "UNKNOWN";
      typeCounts[type] = (typeCounts[type] || 0) + 1;
    });
    return Object.entries(typeCounts).sort(([, a], [, b]) => b - a)[0]?.[0] || "UNKNOWN";
  };

  return (
    <DashboardLayout title="Vendors">
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Vendors</h1>
            <p className="text-muted-foreground">View and manage your vendor information</p>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {stats.data ? (
            <>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Vendors</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.data.total}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">With Invoices</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">{stats.data.withInvoices}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">VAT Vendors</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">{stats.data.withVAT}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Non-VAT Vendors</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">{stats.data.withoutVAT}</div>
                </CardContent>
              </Card>
            </>
          ) : (
            <div className="col-span-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center text-muted-foreground">{stats.isLoading ? "Loading stats..." : "Failed to load stats"}</div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Search and Controls */}
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="flex items-center gap-4">
            <div className="flex-1 max-w-md">
              <SearchInput value={searchTerm} onChange={handleSearchChange} placeholder="Search vendors..." className="w-full" />
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Rows per page:</span>
                <Select value={limit.toString()} onValueChange={handleLimitChange}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {[5, 10, 25, 50].map((value) => (
                      <SelectItem key={value} value={value.toString()}>
                        {value}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button variant="outline" size="sm" onClick={() => vendors.refetch()}>
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </div>

            {/* Pagination Controls */}
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handlePrevPage} disabled={cursors.length === 0}>
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <Button variant="outline" size="sm" onClick={handleNextPage} disabled={!vendors.data?.nextCursor}>
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Vendors Data Table */}
        <Card>
          <CardHeader>
            <CardTitle>Vendors</CardTitle>
            <CardDescription>A list of all vendors with their details</CardDescription>
          </CardHeader>
          <CardContent>
            {vendors.isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Loading vendors...</div>
              </div>
            ) : vendors.data ? (
              <>
                {vendors.data.items.length === 0 ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-muted-foreground">No vendors found</div>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <VendorTableHeader sortConfig={sortConfig} onSortChange={handleSortChange} />
                    </TableHeader>
                    <TableBody>
                      {vendors.data.items.map((vendor: any) => (
                        <TableRow key={vendor.id} className="cursor-pointer hover:bg-muted/50" onClick={() => handleVendorClick(vendor.id)}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Building className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <div className="font-medium">{vendor.name}</div>
                                {vendor.contactPerson && <div className="text-xs text-muted-foreground">{vendor.contactPerson}</div>}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4 text-muted-foreground" />
                              <div className="text-sm">
                                <div>{vendor.city}</div>
                                <div className="text-xs text-muted-foreground">{vendor.country}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              {vendor.email && (
                                <div className="flex items-center gap-1 text-xs">
                                  <Mail className="h-3 w-3 text-muted-foreground" />
                                  <span className="truncate max-w-[120px]">{vendor.email}</span>
                                </div>
                              )}
                              {vendor.phone && (
                                <div className="flex items-center gap-1 text-xs">
                                  <Phone className="h-3 w-3 text-muted-foreground" />
                                  <span>{vendor.phone}</span>
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {vendor.vatNumber ? (
                              <Badge variant="outline" className="font-mono text-xs">
                                {vendor.vatNumber}
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground text-sm">-</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {vendor.invoices.length} invoice{vendor.invoices.length !== 1 ? "s" : ""}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="text-sm font-medium">{currency.formatMonetary(calculateTotalAmount(vendor.invoices), "EUR")}</div>
                          </TableCell>
                          <TableCell>
                            {vendor.invoices.length > 0 ? (
                              <Badge variant="outline">{getMostCommonInvoiceKind(vendor.invoices)}</Badge>
                            ) : (
                              <span className="text-muted-foreground text-sm">-</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={(e) => e.stopPropagation()}>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={(e) => handleDeleteClick({ id: vendor.id, name: vendor.name }, e)}
                                  className="text-destructive focus:text-destructive"
                                  disabled={vendor.invoices.length > 0}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </>
            ) : (
              <div className="flex items-center justify-center py-8">
                <div className="text-destructive">Failed to load vendors: {vendors.error?.message}</div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Vendor Details Drawer */}
        <VendorDetailsDrawer
          vendorId={selectedVendorId}
          open={drawerOpen}
          onOpenChange={handleDrawerOpenChange}
          onNavigateNext={handleNavigateNext}
          onNavigatePrevious={handleNavigatePrevious}
          hasNext={hasNext}
          hasPrevious={hasPrevious}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Vendor</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete "{vendorToDelete?.name}"? This action cannot be undone.
                {vendorToDelete && (vendors.data?.items.find((v) => v.id === vendorToDelete.id)?.invoices ?? []).length > 0 && (
                  <div className="mt-2 text-destructive">This vendor has existing invoices and cannot be deleted.</div>
                )}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteConfirm}
                disabled={!!deleteVendor.isPending}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                {deleteVendor.isPending ? "Deleting..." : "Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </DashboardLayout>
  );
}
