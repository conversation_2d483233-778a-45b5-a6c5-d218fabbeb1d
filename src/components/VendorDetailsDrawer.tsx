import { trpc } from "@/utils/trpc";
import { useEffect, useState } from "react";
import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle } from "@/components/ui/drawer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Building, MapPin, Phone, Mail, CreditCard, FileText, ExternalLink, X, Edit } from "lucide-react";
import { VendorEditForm } from "./VendorEditForm";

interface VendorDetailsDrawerProps {
  vendorId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onNavigateNext?: () => void;
  onNavigatePrevious?: () => void;
  hasNext?: boolean;
  hasPrevious?: boolean;
}

export function VendorDetailsDrawer({
  vendorId,
  open,
  onOpenChange,
  onNavigateNext,
  onNavigatePrevious,
  hasNext = false,
  hasPrevious = false,
}: VendorDetailsDrawerProps) {
  const [isEditing, setIsEditing] = useState(false);

  const vendor = trpc.vendors.getById.useQuery(
    { id: vendorId! },
    {
      enabled: !!vendorId,
    }
  );

  // Reset edit mode when vendor changes
  useEffect(() => {
    setIsEditing(false);
  }, [vendorId]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!open || isEditing) return; // Disable navigation when editing

      if (event.key === "ArrowLeft" && hasPrevious && onNavigatePrevious) {
        event.preventDefault();
        onNavigatePrevious();
      } else if (event.key === "ArrowRight" && hasNext && onNavigateNext) {
        event.preventDefault();
        onNavigateNext();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [open, hasNext, hasPrevious, onNavigateNext, onNavigatePrevious, isEditing]);

  if (!vendorId) return null;

  return (
    <Drawer open={open} onOpenChange={onOpenChange} direction="right">
      <DrawerContent className="h-full !w-3/5 !max-w-none sm:!w-1/2 lg:!w-2/5">
        <DrawerHeader className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <DrawerTitle className="text-xl">{isEditing ? "Edit Vendor" : "Vendor Details"}</DrawerTitle>
              <DrawerDescription>{isEditing ? "Update vendor information" : "View vendor information and related invoices"}</DrawerDescription>
            </div>
            <div className="flex items-center gap-2">
              {!isEditing && vendor.data && (
                <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                  <Edit className="h-4 w-4" />
                </Button>
              )}
              <DrawerClose asChild>
                <Button variant="ghost" size="sm">
                  <X className="h-4 w-4" />
                </Button>
              </DrawerClose>
            </div>
          </div>
        </DrawerHeader>

        {vendor.isLoading ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-muted-foreground">Loading vendor details...</div>
          </div>
        ) : vendor.error ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-destructive">Failed to load vendor: {vendor.error.message}</div>
          </div>
        ) : vendor.data ? (
          <div className="space-y-6 mt-6 px-4 pb-4 overflow-y-auto text-selectable">
            {isEditing ? (
              <VendorEditForm vendor={vendor.data} onSave={() => setIsEditing(false)} onCancel={() => setIsEditing(false)} />
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    {vendor.data.name}
                  </CardTitle>
                  <CardDescription>
                    {vendor.data.invoices.length} invoice{vendor.data.invoices.length !== 1 ? "s" : ""} on record
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Address */}
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 mt-1 text-muted-foreground" />
                    <div className="text-sm">
                      <div>
                        {vendor.data.street} {vendor.data.houseNumber}
                      </div>
                      <div>
                        {vendor.data.zip && `${vendor.data.zip} `}
                        {vendor.data.city}
                      </div>
                      <div>{vendor.data.country}</div>
                    </div>
                  </div>

                  {/* Contact Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {vendor.data.email && (
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{vendor.data.email}</span>
                      </div>
                    )}
                    {vendor.data.phone && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{vendor.data.phone}</span>
                      </div>
                    )}
                  </div>

                  {/* Business Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {vendor.data.vatNumber && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">VAT Number</label>
                        <div className="text-sm">{vendor.data.vatNumber}</div>
                      </div>
                    )}
                    {vendor.data.bankAccount && (
                      <div className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{vendor.data.bankAccount}</span>
                      </div>
                    )}
                  </div>

                  {vendor.data.contactPerson && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Contact Person</label>
                      <div className="text-sm">{vendor.data.contactPerson}</div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Related Invoices */}
            {vendor.data.invoices && vendor.data.invoices.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Related Invoices
                  </CardTitle>
                  <CardDescription>Invoices from this vendor</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Invoice Ref</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>VAT Status</TableHead>
                        <TableHead>Import Type</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {vendor.data.invoices.map((invoice) => (
                        <TableRow key={invoice.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Button variant="outline" size="sm" asChild>
                                <a href={`/invoices?invoiceId=${invoice.id}`} target="_blank" rel="noopener noreferrer">
                                  <ExternalLink className="h-4 w-4" />
                                </a>
                              </Button>
                              <span className="font-mono text-sm">{invoice.invoiceReference}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">{invoice.date.toLocaleDateString()}</TableCell>
                          <TableCell className="text-right">
                            <div className="text-sm font-medium">€{Number(invoice.amountGross).toFixed(2)}</div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Badge variant={invoice.hasVAT ? "default" : "secondary"}>{invoice.hasVAT ? "With VAT" : "No VAT"}</Badge>
                              {invoice.isReverseCharge && <Badge variant="outline">Reverse Charge</Badge>}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{invoice.importItem.importItemType}</Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}
          </div>
        ) : null}
      </DrawerContent>
    </Drawer>
  );
}
