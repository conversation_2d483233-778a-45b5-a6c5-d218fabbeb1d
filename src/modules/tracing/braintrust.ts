import { envVars } from "@/envVars";
import { init<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Span, StartSpanArgs, traced } from "braintrust";
import { Result, ResultAsync } from "neverthrow";
import { MaybePromise } from "p-map";

namespace global {
  export let logger: Logger<any> | undefined = undefined;
}

const project = "albert";

export namespace braintrust {
  export async function getLogger(args?: { projectName?: string }) {
    if (global.logger) {
      return global.logger;
    }

    const logger = await initLogger({
      projectName: project,
      apiKey: envVars.BRAINTRUST_API_KEY,
    });

    global.logger = logger;

    return logger;
  }

  // export async function trace<T>(name: string, fn: (span: Span) => MaybePromise<T>): Promise<T> {
  //   const logger = await getLogger();

  //   return traced(fn, {
  //     name,
  //   });
  // }

  // export function trace<T>(fn: (span: Span) => Promise<T>, args?: StartSpanArgs): (span: Span) => Promise<T> {
  //   return async (span: Span) => {
  //     const logger = await getLogger();
  //     return logger.traced(fn, args);
  //   };
  // }

  type TraceCallback<T> = (span: Span) => MaybePromise<T>;

  export function traceAsyncResult<T>(name: string, fn: (span: Span) => Promise<Result<T, unknown>>): ResultAsync<T, unknown> {
    return ResultAsync.fromSafePromise(trace(name, async (span) => fn(span))).andThen((r) => r);
  }

  export async function trace<T>(name: string, fn: TraceCallback<T>): Promise<T>;
  export async function trace<T>(args: StartSpanArgs, fn: TraceCallback<T>): Promise<T>;
  export function trace<T>(fn: TraceCallback<T>, args?: StartSpanArgs): Promise<T>;
  // export async function trace<T>(arg1: string | TraceCallback<T> | StartSpanArgs, arg2?: TraceCallback<T> | StartSpanArgs): Promise<T> {
  //   let firstTrace = false;
  //   if (!global.logger) {
  //     const logger = await getLogger();
  //     firstTrace = true;
  //   }

  //   let args: StartSpanArgs = typeof arg2 === "object" ? arg2 : {};
  //   let fn: TraceCallback<T> = typeof arg2 === "function" ? (arg2 as TraceCallback<T>) : (arg1 as TraceCallback<T>);

  //   if (typeof arg1 === "string") {
  //     args.name = arg1;
  //   } else if (typeof arg1 === "function") {
  //     fn = arg1 as TraceCallback<T>;
  //   } else {
  //     args = arg1;
  //   }

  //   if (!args.name && fn.name) {
  //     args.name = fn.name;
  //   }

  //   return traced(async (span) => {
  //     if (firstTrace) {
  //       // console.log(`trace started: https://www.braintrust.dev/app/MattDev/p/${project}/logs?r=${span.id}`);
  //     }
  //     return fn(span);
  //   }, args);
  // }

  export async function trace<T>(arg1: string | TraceCallback<T> | StartSpanArgs, arg2?: TraceCallback<T> | StartSpanArgs): Promise<T> {
    let firstTrace = false;
    if (!global.logger) {
      // const logger = await getLogger();
      firstTrace = true;
    }

    let args: StartSpanArgs = typeof arg2 === "object" ? arg2 : {};
    let fn: TraceCallback<T> = typeof arg2 === "function" ? (arg2 as TraceCallback<T>) : (arg1 as TraceCallback<T>);

    if (typeof arg1 === "string") {
      args.name = arg1;
    } else if (typeof arg1 === "function") {
      fn = arg1 as TraceCallback<T>;
    } else {
      args = arg1;
    }

    if (!args.name && fn.name) {
      args.name = fn.name;
    }

    return traced(async (span) => {
      if (firstTrace) {
        // console.log(`trace started: https://www.braintrust.dev/app/MattDev/p/${project}/logs?r=${span.id}`);
      }
      return fn(span);
    }, args);
  }
}
