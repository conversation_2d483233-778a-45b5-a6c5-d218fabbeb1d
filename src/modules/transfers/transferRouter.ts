import { z } from "zod";
import { createTR<PERSON>Router, publicProcedure } from "@/server/trpc";
import { TRPCError } from "@trpc/server";

const getTransferByIdSchema = z.object({
  id: z.string(),
});

const searchTransfersSchema = z.object({
  search: z.string().min(1),
  limit: z.number().min(1).max(50).default(20),
  excludeReconciled: z.boolean().default(true),
});

const getSuggestedMatchesSchema = z.object({
  invoiceId: z.string(),
  limit: z.number().min(1).max(10).default(5),
});

export const transferRouter = createTRPCRouter({
  search: publicProcedure.input(searchTransfersSchema).query(async ({ input, ctx }) => {
    try {
      const { search, limit, excludeReconciled } = input;

      // Build where clause for search
      const whereClause: any = {
        OR: [
          // Search in transfer description
          {
            description: {
              search: search,
            },
          },
          // Search in transfer counterparty
          {
            counterparty: {
              search: search,
            },
          },
        ],
      };

      // Exclude already reconciled transfers if requested
      if (excludeReconciled) {
        whereClause.reconciliations = {
          none: {
            status: {
              in: ["AUTO_MATCHED", "MANUALLY_MATCHED"],
            },
          },
        };
      }

      const transfers = await ctx.prisma.transfer.findMany({
        where: whereClause,
        include: {
          transaction: {
            select: {
              id: true,
              executedAt: true,
              account: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                },
              },
            },
          },
          reconciliations: {
            where: {
              status: {
                in: ["AUTO_MATCHED", "MANUALLY_MATCHED"],
              },
            },
            select: {
              id: true,
              status: true,
              confidenceScore: true,
              invoice: {
                select: {
                  id: true,
                  invoiceReference: true,
                  amountGross: true,
                  currencyCode: true,
                  vendor: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          transaction: {
            executedAt: "desc",
          },
        },
        take: limit,
      });

      return transfers;
    } catch (error) {
      console.error("Failed to search transfers:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to search transfers",
        cause: error,
      });
    }
  }),

  getSuggestedMatches: publicProcedure.input(getSuggestedMatchesSchema).query(async ({ input, ctx }) => {
    try {
      const { invoiceId, limit } = input;

      // Get the invoice details
      const invoice = await ctx.prisma.invoice.findUnique({
        where: { id: invoiceId },
        select: {
          id: true,
          date: true,
          amountGross: true,
          currencyCode: true,
          vendor: {
            select: { name: true },
          },
        },
      });

      if (!invoice) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Invoice not found",
        });
      }

      // Get unmatched transfers (excluding wallet accounts)
      const unmatchedTransfers = await ctx.prisma.transfer.findMany({
        where: {
          reconciliations: {
            none: {},
          },
          transaction: {
            account: {
              type: { not: "WALLET" },
            },
          },
        },
        include: {
          transaction: {
            select: {
              id: true,
              executedAt: true,
              account: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                },
              },
            },
          },
          reconciliations: {
            where: {
              status: {
                in: ["AUTO_MATCHED", "MANUALLY_MATCHED"],
              },
            },
            select: {
              id: true,
              status: true,
              confidenceScore: true,
              invoice: {
                select: {
                  id: true,
                  invoiceReference: true,
                  amountGross: true,
                  currencyCode: true,
                  vendor: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: {
          transaction: {
            executedAt: "desc",
          },
        },
      });

      // Import the scoring function
      const { calculateConfidenceScore, DEFAULT_CONFIG } = await import("@/modules/reconciliation/reconciliationService");

      // Calculate scores for all transfers and find the best matches
      const scoredMatches = [];

      for (const transfer of unmatchedTransfers) {
        try {
          const detailedScore = await calculateConfidenceScore(
            { ...invoice, vendorName: invoice.vendor?.name },
            transfer,
            transfer.transaction,
            DEFAULT_CONFIG
          );

          // Only include matches with a reasonable score (above 30 to show some potential matches)
          if (detailedScore.totalScore >= 30) {
            scoredMatches.push({
              transfer,
              score: detailedScore.totalScore,
              matchReason: detailedScore.overallReason,
              detailedScore,
            });
          }
        } catch (error) {
          console.error(`Error calculating score for transfer ${transfer.id}:`, error);
          // Continue with other transfers
        }
      }

      // Sort by score descending and take the top matches
      const topMatches = scoredMatches
        .sort((a, b) => b.score - a.score)
        .slice(0, limit)
        .map((match) => ({
          ...match.transfer,
          confidenceScore: match.score,
          matchReason: match.matchReason,
          detailedScore: match.detailedScore,
        }));

      return topMatches;
    } catch (error) {
      console.error("Failed to get suggested matches:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get suggested matches",
        cause: error,
      });
    }
  }),

  getById: publicProcedure.input(getTransferByIdSchema).query(async ({ input, ctx }) => {
    try {
      const transfer = await ctx.prisma.transfer.findUnique({
        where: { id: input.id },
        include: {
          transaction: {
            select: {
              id: true,
              executedAt: true,
              account: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                },
              },
            },
          },
          reconciliations: {
            where: {
              status: {
                in: ["AUTO_MATCHED", "MANUALLY_MATCHED"],
              },
            },
            select: {
              id: true,
              status: true,
              confidenceScore: true,
              invoice: {
                select: {
                  id: true,
                  invoiceReference: true,
                  amountGross: true,
                  currencyCode: true,
                  vendor: {
                    select: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      if (!transfer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Transfer not found",
        });
      }

      return transfer;
    } catch (error) {
      console.error("Failed to fetch transfer:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch transfer",
        cause: error,
      });
    }
  }),
});
